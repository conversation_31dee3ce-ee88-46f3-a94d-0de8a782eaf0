#!/bin/bash

# Debug script to understand why staging deployment is failing

set -e

echo "🔍 Debugging Staging Deployment Issue"
echo "====================================="

# Set environment
export ENVIRONMENT=staging
export CERTIFICATE_ARN="arn:aws:acm:us-west-2:123456789012:certificate/debug-test"

echo ""
echo "📋 Environment Variables:"
echo "ENVIRONMENT: $ENVIRONMENT"
echo "CERTIFICATE_ARN: $CERTIFICATE_ARN"

echo ""
echo "📋 samconfig.toml staging section:"
echo "=================================="
sed -n '/\[staging\.deploy\.parameters\]/,/^$/p' samconfig.toml

echo ""
echo "📋 Testing SAM build for staging:"
echo "================================="
sam build --config-env staging

echo ""
echo "📋 Testing SAM deploy (dry run):"
echo "================================"
# Try to see what parameters SAM would use
sam deploy --config-env staging \
    --parameter-overrides "Environment=staging" "CertificateArn=$CERTIFICATE_ARN" \
    --no-execute-changeset \
    --no-confirm-changeset || echo "Deploy test failed"

echo ""
echo "📋 Current CloudFormation exports:"
echo "=================================="
aws cloudformation list-exports --query 'Exports[?starts_with(Name, `gameflex`)].Name' --output table

echo ""
echo "🔍 Debug complete"
