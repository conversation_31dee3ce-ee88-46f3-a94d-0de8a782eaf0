#!/bin/bash

# GameFlex Backend - Production Deployment Script
# This script deploys the SAM application to the production environment

set -e  # Exit on any error

echo "🚀 Starting GameFlex Backend Production Deployment..."

# Check if required environment variables are set
if [ -z "$CERTIFICATE_ARN" ]; then
    echo "❌ Error: CERTIFICATE_ARN environment variable is required for production deployment"
    echo "   Please set it to your ACM certificate ARN for *.gameflex.io"
    echo "   Example: export CERTIFICATE_ARN='arn:aws:acm:us-west-2:123456789012:certificate/12345678-1234-1234-1234-123456789012'"
    exit 1
fi

# Optional: Check if AWS credentials are configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ Error: AWS credentials not configured or invalid"
    echo "   Please run 'aws configure' or set AWS environment variables"
    exit 1
fi

# Production safety check
echo "⚠️  PRODUCTION DEPLOYMENT WARNING ⚠️"
echo "You are about to deploy to the PRODUCTION environment."
echo "This will affect live users and data."
echo ""
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "$confirm" != "yes" ]; then
    echo "❌ Deployment cancelled"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Build the SAM application
echo "🔨 Building SAM application..."
sam build --config-env production

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully"

# Deploy to production
echo "🚀 Deploying to production environment..."
echo "   - Stack: gameflex-production"
echo "   - Domain: api.gameflex.io"
echo "   - Media Domain: media.gameflex.io"
echo "   - Certificate: $CERTIFICATE_ARN"

sam deploy --config-env production \
    --parameter-overrides \
        "CertificateArn=$CERTIFICATE_ARN"

if [ $? -ne 0 ]; then
    echo "❌ Deployment failed"
    exit 1
fi

echo "✅ Deployment completed successfully!"

# Get the outputs
echo "📋 Deployment Information:"
echo "=========================="

# Get stack outputs
STACK_NAME="gameflex-production"
API_URL=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

USER_POOL_CLIENT_ID=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

R2_SECRET_NAME=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`R2SecretName`].OutputValue' \
    --output text 2>/dev/null || echo "Not available")

echo "API Gateway URL: $API_URL"
echo "User Pool ID: $USER_POOL_ID"
echo "User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "R2 Secret Name: $R2_SECRET_NAME"
echo ""

# Instructions for next steps
echo "🔧 Next Steps:"
echo "=============="
echo "1. Configure your DNS to point api.gameflex.io to the API Gateway"
echo "2. Configure your DNS to point media.gameflex.io to your CloudFlare R2 bucket"
echo "3. Update the R2 secret in AWS Secrets Manager with your CloudFlare R2 credentials:"
echo "   aws secretsmanager put-secret-value \\"
echo "     --secret-id '$R2_SECRET_NAME' \\"
echo "     --secret-string '{\"accountId\":\"YOUR_R2_ACCOUNT_ID\",\"accessKeyId\":\"YOUR_R2_ACCESS_KEY\",\"secretAccessKey\":\"YOUR_R2_SECRET_KEY\",\"endpoint\":\"https://YOUR_ACCOUNT_ID.r2.cloudflarestorage.com\",\"bucketName\":\"gameflex-production\",\"publicUrl\":\"https://media.gameflex.io\"}'"
echo ""
echo "4. Test the deployment:"
echo "   curl $API_URL/health"
echo ""
echo "🎉 Production deployment complete!"
