version = 0.1

# Development Environment Configuration
[default.deploy.parameters]
stack_name = "gameflex-development"
resolve_s3 = true
s3_prefix = "gameflex-development"
region = "us-west-2"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"development\" ProjectName=\"gameflex\" R2AccountId=\"\" R2AccessKeyId=\"\" R2SecretAccessKey=\"\" R2Endpoint=\"\" R2BucketName=\"gameflex-development\" R2PublicUrl=\"https://pub-YOUR_R2_SUBDOMAIN.r2.dev\""
image_repositories = []

[default.build.parameters]
cached = true
parallel = true

# Development Environment Variables
[default.global.parameters]
environment_variables = """
ENVIRONMENT=development
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://YOUR_DEV_API_GATEWAY_URL.execute-api.us-west-2.amazonaws.com/Dev
API_BASE_URL=https://YOUR_DEV_API_GATEWAY_URL.execute-api.us-west-2.amazonaws.com/Dev
DEBUG=1
USER_POOL_ID=YOUR_USER_POOL_ID
USER_POOL_CLIENT_ID=YOUR_USER_POOL_CLIENT_ID
USERS_TABLE=gameflex-development-Users
POSTS_TABLE=gameflex-development-Posts
MEDIA_TABLE=gameflex-development-Media
USER_PROFILES_TABLE=gameflex-development-UserProfiles
COMMENTS_TABLE=gameflex-development-Comments
LIKES_TABLE=gameflex-development-Likes
FOLLOWS_TABLE=gameflex-development-Follows
CHANNELS_TABLE=gameflex-development-Channels
CHANNEL_MEMBERS_TABLE=gameflex-development-ChannelMembers
REFLEXES_TABLE=gameflex-development-Reflexes
CHANNELS_TABLE=gameflex-development-Channels
CHANNEL_MEMBERS_TABLE=gameflex-development-ChannelMembers
REFLEXES_TABLE=gameflex-development-Reflexes
R2_SECRET_NAME=gameflex-r2-config-development
APP_CONFIG_SECRET_NAME=gameflex-app-config-development
"""

[default.local_start_api.parameters]
warm_containers = "EAGER"
skip_pull_image = true
docker_network = "sam-local"

[default.local_start_lambda.parameters]
warm_containers = "EAGER"

# Staging Environment Configuration
[staging.deploy.parameters]
stack_name = "gameflex-staging"
resolve_s3 = true
s3_prefix = "gameflex-staging"
region = "us-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"staging\" ProjectName=\"gameflex\" DomainName=\"staging.api.gameflex.io\" CertificateArn=\"\" R2AccountId=\"\" R2AccessKeyId=\"\" R2SecretAccessKey=\"\" R2Endpoint=\"\" R2BucketName=\"gameflex-staging\" R2PublicUrl=\"https://staging.media.gameflex.io\""
image_repositories = []

[staging.build.parameters]
cached = true
parallel = true

# Staging Environment Variables
[staging.global.parameters]
environment_variables = """
ENVIRONMENT=staging
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://staging.api.gameflex.io
API_BASE_URL=https://staging.api.gameflex.io
DEBUG=0
USER_POOL_ID=
USER_POOL_CLIENT_ID=
USERS_TABLE=gameflex-staging-Users
POSTS_TABLE=gameflex-staging-Posts
MEDIA_TABLE=gameflex-staging-Media
USER_PROFILES_TABLE=gameflex-staging-UserProfiles
COMMENTS_TABLE=gameflex-staging-Comments
LIKES_TABLE=gameflex-staging-Likes
FOLLOWS_TABLE=gameflex-staging-Follows
CHANNELS_TABLE=gameflex-staging-Channels
CHANNEL_MEMBERS_TABLE=gameflex-staging-ChannelMembers
REFLEXES_TABLE=gameflex-staging-Reflexes
R2_SECRET_NAME=gameflex-r2-config-staging
APP_CONFIG_SECRET_NAME=gameflex-app-config-staging
DOMAIN_NAME=staging.api.gameflex.io
MEDIA_DOMAIN=staging.media.gameflex.io
SSL_ENABLED=true
FORCE_HTTPS=true
"""

# Production Environment Configuration
[production.deploy.parameters]
stack_name = "gameflex-production"
resolve_s3 = true
s3_prefix = "gameflex-production"
region = "us-west-2"
confirm_changeset = false
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"production\" ProjectName=\"gameflex\" DomainName=\"api.gameflex.io\" CertificateArn=\"\" R2BucketName=\"gameflex-production\" R2PublicUrl=\"https://media.gameflex.io\""
image_repositories = []

[production.build.parameters]
cached = true
parallel = true

# Production Environment Variables
[production.global.parameters]
environment_variables = """
ENVIRONMENT=production
PROJECT_NAME=gameflex
AWS_REGION=us-west-2
API_PORT=3000
API_URL_REMOTE=https://api.gameflex.io
API_BASE_URL=https://api.gameflex.io
DEBUG=0
USER_POOL_ID=
USER_POOL_CLIENT_ID=
USERS_TABLE=gameflex-production-Users
POSTS_TABLE=gameflex-production-Posts
MEDIA_TABLE=gameflex-production-Media
USER_PROFILES_TABLE=gameflex-production-UserProfiles
COMMENTS_TABLE=gameflex-production-Comments
LIKES_TABLE=gameflex-production-Likes
FOLLOWS_TABLE=gameflex-production-Follows
CHANNELS_TABLE=gameflex-production-Channels
CHANNEL_MEMBERS_TABLE=gameflex-production-ChannelMembers
REFLEXES_TABLE=gameflex-production-Reflexes
R2_SECRET_NAME=gameflex-r2-config-production
APP_CONFIG_SECRET_NAME=gameflex-app-config-production
DOMAIN_NAME=api.gameflex.io
MEDIA_DOMAIN=media.gameflex.io
SSL_ENABLED=true
FORCE_HTTPS=true
"""
