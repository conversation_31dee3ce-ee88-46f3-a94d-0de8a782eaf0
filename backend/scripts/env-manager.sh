#!/bin/bash

# GameFlex Backend - Environment Manager
# Utility script to manage environment-specific .env files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"

# Available environments
ENVIRONMENTS=("development" "staging" "production")

# Show usage
show_usage() {
    echo "GameFlex Backend Environment Manager"
    echo ""
    echo "Usage: $0 <command> [environment]"
    echo ""
    echo "Commands:"
    echo "  init                   Initialize samconfig.toml from example"
    echo "  list                   List environment configurations in samconfig.toml"
    echo "  show <env>             Show environment configuration from samconfig.toml"
    echo "  validate <env>         Validate environment configuration"
    echo "  switch <env>           Switch to environment (sets ENVIRONMENT variable)"
    echo "  update-from-stack <env> Update samconfig.toml with CloudFormation outputs"
    echo "  test-load <env>        Test loading environment variables"
    echo ""
    echo "Environments: ${ENVIRONMENTS[*]}"
    echo ""
    echo "Examples:"
    echo "  $0 init                # Create samconfig.toml from example"
    echo "  $0 show staging"
    echo "  $0 validate staging"
    echo "  $0 switch staging"
    echo "  $0 update-from-stack staging"
    echo "  $0 test-load staging"
    echo ""
    echo "Note: Environment variables are now stored in samconfig.toml"
    echo "      Sensitive credentials should not be committed to version control"
}

# Initialize samconfig.toml from example
init_samconfig() {
    local samconfig_file="$BACKEND_DIR/samconfig.toml"
    local example_file="$BACKEND_DIR/samconfig.toml.example"

    if [ -f "$samconfig_file" ]; then
        print_warning "samconfig.toml already exists"
        read -p "Overwrite? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_status "Cancelled"
            return 0
        fi
        # Create backup
        cp "$samconfig_file" "$samconfig_file.backup"
        print_status "Backup saved as samconfig.toml.backup"
    fi

    if [ ! -f "$example_file" ]; then
        print_error "samconfig.toml.example not found"
        return 1
    fi

    # Copy example to actual config
    cp "$example_file" "$samconfig_file"
    print_success "✅ Created samconfig.toml from example"

    echo ""
    print_warning "⚠️  IMPORTANT: Update the following placeholders with your actual values:"
    echo ""
    echo "🔑 Required for Development:"
    echo "  - YOUR_R2_ACCOUNT_ID"
    echo "  - YOUR_R2_ACCESS_KEY_ID"
    echo "  - YOUR_R2_SECRET_ACCESS_KEY"
    echo "  - YOUR_R2_SUBDOMAIN"
    echo "  - YOUR_CLOUDFLARE_API_TOKEN"
    echo ""
    echo "🚀 Will be populated after deployment:"
    echo "  - YOUR_DEV_API_GATEWAY_URL"
    echo "  - YOUR_USER_POOL_ID"
    echo "  - YOUR_USER_POOL_CLIENT_ID"
    echo ""
    echo "📝 Edit samconfig.toml now to add your credentials"
    echo "   Do NOT commit real credentials to version control!"
}

# List environment configurations
list_env_configs() {
    print_status "Environment configurations in samconfig.toml:"
    echo ""

    if [ ! -f "$BACKEND_DIR/samconfig.toml" ]; then
        print_error "samconfig.toml not found"
        return 1
    fi

    for env in "${ENVIRONMENTS[@]}"; do
        local section_name
        case "$env" in
            "development") section_name="default" ;;
            *) section_name="$env" ;;
        esac

        if grep -q "^\[${section_name}\.global\.parameters\]" "$BACKEND_DIR/samconfig.toml"; then
            print_success "✓ $env (configured in samconfig.toml)"
        else
            print_warning "✗ $env (not configured)"
        fi
    done

    echo ""
    print_status "Legacy .env files (if any):"
    for env in "${ENVIRONMENTS[@]}"; do
        local env_file="$BACKEND_DIR/.env.$env"
        if [ -f "$env_file" ]; then
            print_warning "⚠️  .env.$env (legacy - consider removing)"
        fi
    done

    if [ -f "$BACKEND_DIR/.env" ]; then
        print_warning "⚠️  .env (legacy - consider removing)"
    fi
}

# Create environment file from template
create_env_file() {
    local env="$1"
    local env_file="$BACKEND_DIR/.env.$env"
    local template_file="$BACKEND_DIR/.env.example"
    
    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi
    
    if [ -f "$env_file" ]; then
        print_warning ".env.$env already exists"
        read -p "Overwrite? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_status "Cancelled"
            exit 0
        fi
    fi
    
    if [ ! -f "$template_file" ]; then
        print_error "Template file .env.example not found"
        exit 1
    fi
    
    # Copy template and customize for environment
    cp "$template_file" "$env_file"
    
    # Update environment-specific values
    sed -i "s/ENVIRONMENT=development/ENVIRONMENT=$env/" "$env_file"
    
    case "$env" in
        "staging")
            sed -i "s|API_URL_REMOTE=.*|API_URL_REMOTE=https://staging.api.gameflex.io|" "$env_file"
            sed -i "s|API_BASE_URL=.*|API_BASE_URL=https://staging.api.gameflex.io|" "$env_file"
            sed -i "s|R2_BUCKET_NAME=.*|R2_BUCKET_NAME=gameflex-staging|" "$env_file"
            sed -i "s|R2_PUBLIC_URL=.*|R2_PUBLIC_URL=https://staging.media.gameflex.io|" "$env_file"
            sed -i "s/DEBUG=1/DEBUG=0/" "$env_file"
            ;;
        "production")
            sed -i "s|API_URL_REMOTE=.*|API_URL_REMOTE=https://api.gameflex.io|" "$env_file"
            sed -i "s|API_BASE_URL=.*|API_BASE_URL=https://api.gameflex.io|" "$env_file"
            sed -i "s|R2_BUCKET_NAME=.*|R2_BUCKET_NAME=gameflex-production|" "$env_file"
            sed -i "s|R2_PUBLIC_URL=.*|R2_PUBLIC_URL=https://media.gameflex.io|" "$env_file"
            sed -i "s/DEBUG=1/DEBUG=0/" "$env_file"
            ;;
    esac
    
    print_success "Created .env.$env"
    print_status "Remember to update the values with your actual configuration"
}

# Copy environment file
copy_env_file() {
    local from="$1"
    local to="$2"
    local from_file="$BACKEND_DIR/.env.$from"
    local to_file="$BACKEND_DIR/.env.$to"
    
    if [ -z "$from" ] || [ -z "$to" ]; then
        print_error "Source and destination environments required"
        show_usage
        exit 1
    fi
    
    if [ ! -f "$from_file" ]; then
        print_error ".env.$from does not exist"
        exit 1
    fi
    
    if [ -f "$to_file" ]; then
        print_warning ".env.$to already exists"
        read -p "Overwrite? (y/N): " confirm
        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            print_status "Cancelled"
            exit 0
        fi
    fi
    
    cp "$from_file" "$to_file"
    
    # Update environment name in copied file
    sed -i "s/ENVIRONMENT=$from/ENVIRONMENT=$to/" "$to_file"
    
    print_success "Copied .env.$from to .env.$to"
}

# Validate environment file
validate_env_file() {
    local env="$1"
    local env_file="$BACKEND_DIR/.env.$env"
    
    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi
    
    if [ ! -f "$env_file" ]; then
        print_error ".env.$env does not exist"
        exit 1
    fi
    
    print_status "Validating .env.$env..."
    
    # Required variables
    local required_vars=("ENVIRONMENT" "PROJECT_NAME" "AWS_REGION")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        print_success "✓ All required variables present"
    else
        print_error "✗ Missing required variables: ${missing_vars[*]}"
        exit 1
    fi
}

# Switch environment
switch_environment() {
    local env="$1"
    
    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi
    
    export ENVIRONMENT="$env"
    print_success "Switched to environment: $env"
    print_status "Set ENVIRONMENT=$env in your shell:"
    echo "export ENVIRONMENT=$env"
}

# Show environment configuration
show_env_config() {
    local env="$1"

    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi

    local section_name
    case "$env" in
        "development") section_name="default" ;;
        *) section_name="$env" ;;
    esac

    if [ ! -f "$BACKEND_DIR/samconfig.toml" ]; then
        print_error "samconfig.toml not found"
        exit 1
    fi

    print_status "Environment configuration for $env from samconfig.toml:"
    echo ""

    # Show deploy parameters
    echo "📋 Deploy Parameters:"
    sed -n "/\[${section_name}\.deploy\.parameters\]/,/^\[/p" "$BACKEND_DIR/samconfig.toml" | head -n -1

    echo ""
    echo "📋 Environment Variables:"
    sed -n "/\[${section_name}\.global\.parameters\]/,/^\[/p" "$BACKEND_DIR/samconfig.toml" | head -n -1
}

# Test loading environment variables
test_load_env() {
    local env="$1"

    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi

    print_status "Testing environment variable loading for $env..."

    # Test the load script
    if source "$SCRIPT_DIR/load-sam-env.sh" "$env"; then
        print_success "✅ Successfully loaded environment variables"
        echo ""
        print_status "Key variables loaded:"
        echo "  ENVIRONMENT: ${ENVIRONMENT:-not set}"
        echo "  PROJECT_NAME: ${PROJECT_NAME:-not set}"
        echo "  AWS_REGION: ${AWS_REGION:-not set}"
        echo "  API_BASE_URL: ${API_BASE_URL:-not set}"
        echo "  R2_BUCKET_NAME: ${R2_BUCKET_NAME:-not set}"
    else
        print_error "❌ Failed to load environment variables"
        exit 1
    fi
}

# Update environment file from CloudFormation stack
update_from_stack() {
    local env="$1"
    local env_file="$BACKEND_DIR/.env.$env"
    local stack_name="gameflex-$env"
    
    if [ -z "$env" ]; then
        print_error "Environment name required"
        show_usage
        exit 1
    fi
    
    if [ ! -f "$env_file" ]; then
        print_error ".env.$env does not exist"
        exit 1
    fi
    
    print_status "Updating .env.$env from CloudFormation stack $stack_name..."
    
    # Get stack outputs
    local user_pool_id=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].Outputs[?OutputKey==`UserPoolId`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    local user_pool_client_id=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].Outputs[?OutputKey==`UserPoolClientId`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    local api_url=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].Outputs[?OutputKey==`ApiGatewayUrl`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    # Create backup
    cp "$env_file" "$env_file.backup"
    
    # Update values
    if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ]; then
        sed -i "s/^USER_POOL_ID=.*/USER_POOL_ID=$user_pool_id/" "$env_file"
        print_success "Updated USER_POOL_ID"
    fi
    
    if [ -n "$user_pool_client_id" ] && [ "$user_pool_client_id" != "None" ]; then
        sed -i "s/^USER_POOL_CLIENT_ID=.*/USER_POOL_CLIENT_ID=$user_pool_client_id/" "$env_file"
        print_success "Updated USER_POOL_CLIENT_ID"
    fi
    
    if [ -n "$api_url" ] && [ "$api_url" != "None" ]; then
        sed -i "s|^API_URL_REMOTE=.*|API_URL_REMOTE=$api_url|" "$env_file"
        sed -i "s|^API_BASE_URL=.*|API_BASE_URL=$api_url|" "$env_file"
        print_success "Updated API URLs"
    fi
    
    print_success "Updated .env.$env from CloudFormation stack"
    print_status "Backup saved as .env.$env.backup"
}

# Main script logic
case "$1" in
    "init")
        init_samconfig
        ;;
    "list")
        list_env_configs
        ;;
    "show")
        show_env_config "$2"
        ;;
    "validate")
        validate_env_file "$2"
        ;;
    "switch")
        switch_environment "$2"
        ;;
    "test-load")
        test_load_env "$2"
        ;;
    "update-from-stack")
        update_from_stack "$2"
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
