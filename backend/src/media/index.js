const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');
const { createR2Client, getR2BucketName, getR2PublicUrl } = require('../utils/r2-config');

// Configure AWS SDK for SAM local (DynamoDB only)
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);
const MEDIA_TABLE = process.env.MEDIA_TABLE;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};

// Upload media
const uploadMedia = async (event) => {
    try {
        const { fileName, fileType, fileSize, mediaType } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!fileName || !fileType) {
            return createResponse(400, { error: 'fileName and fileType are required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const mediaId = uuidv4();
        const fileExtension = fileName.split('.').pop();

        // Create R2 key based on media type
        let r2Key;
        if (mediaType === 'avatar') {
            r2Key = `avatars/${userId}/${mediaId}.${fileExtension}`;
        } else {
            r2Key = `media/${userId}/${mediaId}.${fileExtension}`;
        }

        // Get R2 configuration and create client
        const r2Client = await createR2Client();
        const bucketName = await getR2BucketName();

        // Generate presigned URL for R2 upload
        const presignedUrl = r2Client.getSignedUrl('putObject', {
            Bucket: bucketName,
            Key: r2Key,
            ContentType: fileType,
            Expires: 300 // 5 minutes
        });

        // Get public URL
        const publicUrl = await getR2PublicUrl();

        // Create media record
        const mediaRecord = {
            id: mediaId,
            fileName,
            fileType,
            fileSize: fileSize || 0,
            mediaType: mediaType || 'image',
            userId,
            r2Key,
            bucketName,
            url: `${publicUrl}/${r2Key}`,
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: MEDIA_TABLE,
            Item: mediaRecord
        }).promise();

        return createResponse(200, {
            message: 'Upload URL generated successfully',
            mediaId,
            uploadUrl: presignedUrl,
            media: mediaRecord
        });

    } catch (error) {
        console.error('UploadMedia error:', error);
        return createResponse(500, { error: 'Failed to generate upload URL', details: error.message });
    }
};

// Get media
const getMedia = async (event) => {
    try {
        const { id } = event.pathParameters;

        const result = await dynamodb.get({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item;

        // For R2, we use the public URL directly since it's publicly accessible
        // No need for presigned URLs for downloads with R2 public bucket
        if (media.status === 'uploaded' && media.url) {
            media.downloadUrl = media.url;
        }

        return createResponse(200, { media });

    } catch (error) {
        console.error('GetMedia error:', error);
        return createResponse(500, { error: 'Failed to get media', details: error.message });
    }
};

// Delete media
const deleteMedia = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get media record
        const result = await dynamodb.get({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Media not found' });
        }

        const media = result.Item;

        // Delete from R2
        if (media.r2Key && media.bucketName) {
            const r2Client = await createR2Client();
            await r2Client.deleteObject({
                Bucket: media.bucketName,
                Key: media.r2Key
            }).promise();
        }

        // Delete from DynamoDB
        await dynamodb.delete({
            TableName: MEDIA_TABLE,
            Key: { id }
        }).promise();

        return createResponse(200, { message: 'Media deleted successfully' });

    } catch (error) {
        console.error('DeleteMedia error:', error);
        return createResponse(500, { error: 'Failed to delete media', details: error.message });
    }
};

// Update media status (called after successful upload)
const updateMediaStatus = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { status } = JSON.parse(event.body);

        if (!status) {
            return createResponse(400, { error: 'Status is required' });
        }

        const result = await dynamodb.update({
            TableName: MEDIA_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': status,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        }).promise();

        return createResponse(200, {
            message: 'Media status updated successfully',
            media: result.Attributes
        });

    } catch (error) {
        console.error('UpdateMediaStatus error:', error);
        return createResponse(500, { error: 'Failed to update media status', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        if (httpMethod === 'POST' && path === '/media/upload') {
            return await uploadMedia(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id) {
            return await getMedia(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id) {
            return await deleteMedia(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id) {
            return await updateMediaStatus(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
