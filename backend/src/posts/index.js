const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);
const s3 = new AWS.S3(awsConfig);

const POSTS_TABLE = process.env.POSTS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;
const COMMENTS_TABLE = process.env.COMMENTS_TABLE;
const LIKES_TABLE = process.env.LIKES_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const MEDIA_BUCKET = process.env.MEDIA_BUCKET;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Get all posts
const getPosts = async (event) => {
    try {
        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        const result = await dynamodb.scan({
            TableName: POSTS_TABLE
        }).promise();

        // Filter out inactive posts and posts that are not published
        const activePosts = result.Items.filter(post =>
            post.active === true && post.status === 'published'
        );

        // Sort posts by created_at descending
        const posts = activePosts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        // Fetch media data and like status for posts
        const postsWithMediaAndLikes = await Promise.all(posts.map(async (post) => {
            // Fetch media data if post has media_id
            if (post.media_id) {
                try {
                    const mediaResult = await dynamodb.get({
                        TableName: MEDIA_TABLE,
                        Key: { id: post.media_id }
                    }).promise();

                    if (mediaResult.Item) {
                        post.media = mediaResult.Item;
                    }
                } catch (error) {
                    console.error(`Failed to fetch media for post ${post.id}:`, error);
                    // Continue without media data if fetch fails
                }
            }

            // Check if current user has liked this post
            let isLikedByCurrentUser = false;
            if (currentUserId) {
                try {
                    const likeResult = await dynamodb.get({
                        TableName: LIKES_TABLE,
                        Key: {
                            post_id: post.id,
                            user_id: currentUserId
                        }
                    }).promise();

                    isLikedByCurrentUser = !!likeResult.Item;
                } catch (error) {
                    console.error(`Failed to check like status for post ${post.id}:`, error);
                    // Continue with false if check fails
                }
            }

            // Add the like status to the post
            post.is_liked_by_current_user = isLikedByCurrentUser;

            return post;
        }));

        return createResponse(200, {
            posts: postsWithMediaAndLikes,
            count: postsWithMediaAndLikes.length
        });

    } catch (error) {
        console.error('GetPosts error:', error);
        return createResponse(500, { error: 'Failed to get posts', details: error.message });
    }
};

// Get single post
const getPost = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get current user ID from authorizer context
        const currentUserId = getUserIdFromContext(event);

        const result = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!result.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const post = result.Item;

        // Fetch media data if post has media_id
        if (post.media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: post.media_id }
                }).promise();

                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            } catch (error) {
                console.error(`Failed to fetch media for post ${post.id}:`, error);
                // Continue without media data if fetch fails
            }
        }

        // Check if current user has liked this post
        let isLikedByCurrentUser = false;
        if (currentUserId) {
            try {
                const likeResult = await dynamodb.get({
                    TableName: LIKES_TABLE,
                    Key: {
                        post_id: post.id,
                        user_id: currentUserId
                    }
                }).promise();

                isLikedByCurrentUser = !!likeResult.Item;
            } catch (error) {
                console.error(`Failed to check like status for post ${post.id}:`, error);
                // Continue with false if check fails
            }
        }

        // Add the like status to the post
        post.is_liked_by_current_user = isLikedByCurrentUser;

        return createResponse(200, { post });

    } catch (error) {
        console.error('GetPost error:', error);
        return createResponse(500, { error: 'Failed to get post', details: error.message });
    }
};

// Helper function to get user ID from authorizer context
const getUserIdFromContext = (event) => {
    // When using Lambda authorizer, user info is available in the context
    if (event.requestContext && event.requestContext.authorizer) {
        return event.requestContext.authorizer.userId;
    }
    return null;
};

// Create draft post (first step of multi-step creation)
const createDraftPost = async (event) => {
    try {
        const { title, content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        const postId = uuidv4();
        const post = {
            id: postId,
            title: title || null,
            content,
            media_id: null, // Will be set later if media is uploaded
            author_id: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'draft', // Status: draft, uploading_media, published
            active: false, // Not active until published
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: POSTS_TABLE,
            Item: post
        }).promise();

        return createResponse(201, {
            message: 'Draft post created successfully',
            post
        });

    } catch (error) {
        console.error('CreateDraftPost error:', error);
        return createResponse(500, { error: 'Failed to create draft post', details: error.message });
    }
};

// Create post (legacy endpoint - now creates and publishes immediately)
const createPost = async (event) => {
    try {
        const { title, content, media_id } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!content) {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // If media_id is provided, verify it exists
        if (media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: media_id }
                }).promise();

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }

        const postId = uuidv4();
        const post = {
            id: postId,
            title: title || null,
            content,
            media_id: media_id || null,
            author_id: authorId,
            userId: authorId, // Keep for backwards compatibility
            likes: 0,
            comments: 0,
            reflexes: 0,
            status: 'published', // Status: draft, uploading_media, published
            active: true, // Final switch to show in feeds
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: POSTS_TABLE,
            Item: post
        }).promise();

        // Fetch media data if media_id was provided
        if (media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: media_id }
                }).promise();

                if (mediaResult.Item) {
                    post.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(201, {
            message: 'Post created successfully',
            post
        });

    } catch (error) {
        console.error('CreatePost error:', error);
        return createResponse(500, { error: 'Failed to create post', details: error.message });
    }
};

// Update post
const updatePost = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { title, content, media_id } = JSON.parse(event.body);

        // If media_id is provided, verify it exists
        if (media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: media_id }
                }).promise();

                if (!mediaResult.Item) {
                    return createResponse(400, { error: 'Invalid media_id: media not found' });
                }
            } catch (error) {
                console.error('Error verifying media:', error);
                return createResponse(400, { error: 'Failed to verify media_id' });
            }
        }

        const updateExpression = [];
        const expressionAttributeValues = {};
        const expressionAttributeNames = {};

        if (title !== undefined) {
            updateExpression.push('#title = :title');
            expressionAttributeNames['#title'] = 'title';
            expressionAttributeValues[':title'] = title;
        }

        if (content !== undefined) {
            updateExpression.push('#content = :content');
            expressionAttributeNames['#content'] = 'content';
            expressionAttributeValues[':content'] = content;
        }

        if (media_id !== undefined) {
            updateExpression.push('media_id = :media_id');
            expressionAttributeValues[':media_id'] = media_id;
        }

        updateExpression.push('updated_at = :updated_at');
        expressionAttributeValues[':updated_at'] = new Date().toISOString();

        const result = await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: `SET ${updateExpression.join(', ')}`,
            ExpressionAttributeNames: expressionAttributeNames,
            ExpressionAttributeValues: expressionAttributeValues,
            ReturnValues: 'ALL_NEW'
        }).promise();

        const updatedPost = result.Attributes;

        // Fetch media data if post has media_id
        if (updatedPost.media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: updatedPost.media_id }
                }).promise();

                if (mediaResult.Item) {
                    updatedPost.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post updated successfully',
            post: updatedPost
        });

    } catch (error) {
        console.error('UpdatePost error:', error);
        return createResponse(500, { error: 'Failed to update post', details: error.message });
    }
};

// Attach media to draft post
const attachMediaToPost = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { media_id } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!media_id) {
            return createResponse(400, { error: 'media_id is required' });
        }

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify media exists and is uploaded
        const mediaResult = await dynamodb.get({
            TableName: MEDIA_TABLE,
            Key: { id: media_id }
        }).promise();

        if (!mediaResult.Item) {
            return createResponse(400, { error: 'Invalid media_id: media not found' });
        }

        if (mediaResult.Item.status !== 'uploaded') {
            return createResponse(400, { error: 'Media upload not completed' });
        }

        // Get the post and verify ownership
        const postResult = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if (postResult.Item.author_id !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // Update post with media and change status to uploading_media
        const result = await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET media_id = :media_id, #status = :status, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':media_id': media_id,
                ':status': 'uploading_media',
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        }).promise();

        return createResponse(200, {
            message: 'Media attached to post successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('AttachMediaToPost error:', error);
        return createResponse(500, { error: 'Failed to attach media to post', details: error.message });
    }
};

// Publish post (final step)
const publishPost = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get user ID from authorizer context
        const authorId = getUserIdFromContext(event);

        if (!authorId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the post and verify ownership
        const postResult = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        if (postResult.Item.author_id !== authorId) {
            return createResponse(403, { error: 'Not authorized to modify this post' });
        }

        // If post has media, verify it's uploaded
        if (postResult.Item.media_id) {
            const mediaResult = await dynamodb.get({
                TableName: MEDIA_TABLE,
                Key: { id: postResult.Item.media_id }
            }).promise();

            if (!mediaResult.Item || mediaResult.Item.status !== 'uploaded') {
                return createResponse(400, { error: 'Media upload not completed' });
            }
        }

        // Update post to published and active
        const result = await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET #status = :status, active = :active, updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status'
            },
            ExpressionAttributeValues: {
                ':status': 'published',
                ':active': true,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        }).promise();

        // Fetch media data if post has media_id
        if (result.Attributes.media_id) {
            try {
                const mediaResult = await dynamodb.get({
                    TableName: MEDIA_TABLE,
                    Key: { id: result.Attributes.media_id }
                }).promise();

                if (mediaResult.Item) {
                    result.Attributes.media = mediaResult.Item;
                }
            } catch (error) {
                console.error('Error fetching media for response:', error);
                // Continue without media data in response
            }
        }

        return createResponse(200, {
            message: 'Post published successfully',
            post: result.Attributes
        });

    } catch (error) {
        console.error('PublishPost error:', error);
        return createResponse(500, { error: 'Failed to publish post', details: error.message });
    }
};

// Delete post
const deletePost = async (event) => {
    try {
        const { id } = event.pathParameters;

        await dynamodb.delete({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        return createResponse(200, { message: 'Post deleted successfully' });

    } catch (error) {
        console.error('DeletePost error:', error);
        return createResponse(500, { error: 'Failed to delete post', details: error.message });
    }
};

// Like post
const likePost = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Check if already liked
        const existingLike = await dynamodb.get({
            TableName: LIKES_TABLE,
            Key: { post_id: id, user_id: userId }
        }).promise();

        if (existingLike.Item) {
            return createResponse(400, { error: 'Post already liked' });
        }

        // Add like
        await dynamodb.put({
            TableName: LIKES_TABLE,
            Item: {
                post_id: id,
                user_id: userId,
                created_at: new Date().toISOString()
            }
        }).promise();

        // Update post likes count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        return createResponse(200, { message: 'Post liked successfully' });

    } catch (error) {
        console.error('LikePost error:', error);
        return createResponse(500, { error: 'Failed to like post', details: error.message });
    }
};

// Unlike post
const unlikePost = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Remove like
        await dynamodb.delete({
            TableName: LIKES_TABLE,
            Key: { post_id: id, user_id: userId }
        }).promise();

        // Update post likes count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD likes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        return createResponse(200, { message: 'Post unliked successfully' });

    } catch (error) {
        console.error('UnlikePost error:', error);
        return createResponse(500, { error: 'Failed to unlike post', details: error.message });
    }
};

// Get comments for a post
const getComments = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Query comments for the post
        const result = await dynamodb.query({
            TableName: COMMENTS_TABLE,
            IndexName: 'post-id-index',
            KeyConditionExpression: 'post_id = :post_id',
            ExpressionAttributeValues: {
                ':post_id': id
            }
        }).promise();

        // Sort comments by created_at ascending (oldest first)
        const comments = result.Items.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

        // Fetch user data for each comment
        const commentsWithUserData = await Promise.all(comments.map(async (comment) => {
            try {
                const userResult = await dynamodb.get({
                    TableName: USERS_TABLE,
                    Key: { id: comment.user_id }
                }).promise();

                if (userResult.Item) {
                    comment.username = userResult.Item.username;
                    comment.display_name = userResult.Item.display_name;
                    comment.avatar_url = userResult.Item.avatar_url;
                }
            } catch (error) {
                console.error(`Failed to fetch user data for comment ${comment.id}:`, error);
                // Continue without user data if fetch fails
            }
            return comment;
        }));

        return createResponse(200, {
            comments: commentsWithUserData,
            count: commentsWithUserData.length
        });

    } catch (error) {
        console.error('GetComments error:', error);
        return createResponse(500, { error: 'Failed to get comments', details: error.message });
    }
};

// Create a comment
const createComment = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Verify the post exists
        const postResult = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        const commentId = uuidv4();
        const comment = {
            id: commentId,
            post_id: id,
            user_id: userId,
            content: content.trim(),
            like_count: 0,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        // Create the comment
        await dynamodb.put({
            TableName: COMMENTS_TABLE,
            Item: comment
        }).promise();

        // Update post comments count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD comments :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        // Fetch user data for the response
        try {
            const userResult = await dynamodb.get({
                TableName: USERS_TABLE,
                Key: { id: userId }
            }).promise();

            if (userResult.Item) {
                comment.username = userResult.Item.username;
                comment.display_name = userResult.Item.display_name;
                comment.avatar_url = userResult.Item.avatar_url;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(201, {
            message: 'Comment created successfully',
            comment
        });

    } catch (error) {
        console.error('CreateComment error:', error);
        return createResponse(500, { error: 'Failed to create comment', details: error.message });
    }
};

// Update a comment
const updateComment = async (event) => {
    try {
        const { id } = event.pathParameters;
        const { content } = JSON.parse(event.body);

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!content || content.trim() === '') {
            return createResponse(400, { error: 'Content is required' });
        }

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership
        const commentResult = await dynamodb.get({
            TableName: COMMENTS_TABLE,
            Key: { id }
        }).promise();

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if (commentResult.Item.user_id !== userId) {
            return createResponse(403, { error: 'You can only update your own comments' });
        }

        // Update the comment
        const result = await dynamodb.update({
            TableName: COMMENTS_TABLE,
            Key: { id },
            UpdateExpression: 'SET content = :content, updated_at = :updated_at',
            ExpressionAttributeValues: {
                ':content': content.trim(),
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        }).promise();

        const updatedComment = result.Attributes;

        // Fetch user data for the response
        try {
            const userResult = await dynamodb.get({
                TableName: USERS_TABLE,
                Key: { id: userId }
            }).promise();

            if (userResult.Item) {
                updatedComment.username = userResult.Item.username;
                updatedComment.display_name = userResult.Item.display_name;
                updatedComment.avatar_url = userResult.Item.avatar_url;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(200, {
            message: 'Comment updated successfully',
            comment: updatedComment
        });

    } catch (error) {
        console.error('UpdateComment error:', error);
        return createResponse(500, { error: 'Failed to update comment', details: error.message });
    }
};

// Delete a comment
const deleteComment = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Get user ID from authorizer context
        const userId = getUserIdFromContext(event);

        if (!userId) {
            return createResponse(401, { error: 'User not authenticated' });
        }

        // Get the comment to verify ownership and get post_id
        const commentResult = await dynamodb.get({
            TableName: COMMENTS_TABLE,
            Key: { id }
        }).promise();

        if (!commentResult.Item) {
            return createResponse(404, { error: 'Comment not found' });
        }

        // Verify the user owns the comment
        if (commentResult.Item.user_id !== userId) {
            return createResponse(403, { error: 'You can only delete your own comments' });
        }

        const postId = commentResult.Item.post_id;

        // Delete the comment
        await dynamodb.delete({
            TableName: COMMENTS_TABLE,
            Key: { id }
        }).promise();

        // Update post comments count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD comments :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        return createResponse(200, { message: 'Comment deleted successfully' });

    } catch (error) {
        console.error('DeleteComment error:', error);
        return createResponse(500, { error: 'Failed to delete comment', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Posts routes
        if (httpMethod === 'GET' && path === '/posts') {
            return await getPosts(event);
        } else if (httpMethod === 'POST' && path === '/posts/draft') {
            return await createDraftPost(event);
        } else if (httpMethod === 'POST' && path === '/posts') {
            return await createPost(event);
        } else if (httpMethod === 'GET' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await getPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/media')) {
            return await attachMediaToPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && path.includes('/publish')) {
            return await publishPost(event);
        } else if (httpMethod === 'PUT' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like') && !path.includes('/media') && !path.includes('/publish')) {
            return await updatePost(event);
        } else if (httpMethod === 'DELETE' && pathParameters && pathParameters.id && !path.includes('/comments') && !path.includes('/like')) {
            return await deletePost(event);
        }
        // Like routes
        else if (httpMethod === 'POST' && path.includes('/like')) {
            return await likePost(event);
        } else if (httpMethod === 'DELETE' && path.includes('/like')) {
            return await unlikePost(event);
        }
        // Comments routes
        else if (httpMethod === 'GET' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await getComments(event);
        } else if (httpMethod === 'POST' && path.includes('/comments') && pathParameters && pathParameters.id) {
            return await createComment(event);
        } else if (httpMethod === 'PUT' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await updateComment(event);
        } else if (httpMethod === 'DELETE' && path.startsWith('/comments/') && pathParameters && pathParameters.id) {
            return await deleteComment(event);
        } else {
            return createResponse(404, { error: 'Not found' });
        }
    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
