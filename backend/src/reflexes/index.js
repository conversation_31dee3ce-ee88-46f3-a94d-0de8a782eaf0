const AWS = require('aws-sdk');
const { v4: uuidv4 } = require('uuid');

// Configure AWS SDK for SAM local
const awsConfig = {
    region: process.env.AWS_REGION || 'us-east-1'
};

const dynamodb = new AWS.DynamoDB.DocumentClient(awsConfig);

const REFLEXES_TABLE = process.env.REFLEXES_TABLE;
const POSTS_TABLE = process.env.POSTS_TABLE;
const USERS_TABLE = process.env.USERS_TABLE;
const MEDIA_TABLE = process.env.MEDIA_TABLE;

// Helper function to create response
const createResponse = (statusCode, body) => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Helper function to get user ID from event
const getUserIdFromEvent = (event) => {
    try {
        if (event.requestContext && event.requestContext.authorizer) {
            const claims = event.requestContext.authorizer.claims;
            if (claims && claims.sub) {
                return claims.sub;
            }
        }
        throw new Error('User ID not found in request context');
    } catch (error) {
        console.error('Error extracting user ID:', error);
        throw new Error('Authentication required');
    }
};

// Get reflexes for a post
const getReflexes = async (event) => {
    try {
        const { id } = event.pathParameters;

        // Verify post exists
        const postResult = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Query reflexes for the post
        const result = await dynamodb.query({
            TableName: REFLEXES_TABLE,
            IndexName: 'post-id-index',
            KeyConditionExpression: 'post_id = :post_id',
            ExpressionAttributeValues: {
                ':post_id': id
            },
            ScanIndexForward: false // Get most recent first
        }).promise();

        // Enrich reflexes with user data
        const reflexesWithUserData = await Promise.all(
            result.Items.map(async (reflex) => {
                try {
                    const userResult = await dynamodb.get({
                        TableName: USERS_TABLE,
                        Key: { id: reflex.user_id }
                    }).promise();

                    if (userResult.Item) {
                        reflex.username = userResult.Item.username;
                        reflex.display_name = userResult.Item.display_name;
                        reflex.avatar_url = userResult.Item.avatar_url;
                    }
                } catch (error) {
                    console.error('Error fetching user data for reflex:', error);
                    // Continue without user data
                }
                return reflex;
            })
        );

        return createResponse(200, {
            reflexes: reflexesWithUserData,
            count: reflexesWithUserData.length
        });

    } catch (error) {
        console.error('GetReflexes error:', error);
        return createResponse(500, { error: 'Failed to get reflexes', details: error.message });
    }
};

// Create a new reflex
const createReflex = async (event) => {
    try {
        const { id } = event.pathParameters; // post_id
        const userId = getUserIdFromEvent(event);
        const body = JSON.parse(event.body || '{}');

        const { 
            media_id, 
            flare_data, 
            text_overlay, 
            reflex_type = 'flare' 
        } = body;

        // Validate required fields based on reflex type
        if (reflex_type === 'custom_image' && !media_id) {
            return createResponse(400, { error: 'media_id is required for custom_image reflexes' });
        }

        if (reflex_type === 'flare' && !flare_data && !text_overlay) {
            return createResponse(400, { error: 'flare_data or text_overlay is required for flare reflexes' });
        }

        // Verify post exists
        const postResult = await dynamodb.get({
            TableName: POSTS_TABLE,
            Key: { id }
        }).promise();

        if (!postResult.Item) {
            return createResponse(404, { error: 'Post not found' });
        }

        // Verify media exists if media_id provided
        if (media_id) {
            const mediaResult = await dynamodb.get({
                TableName: MEDIA_TABLE,
                Key: { id: media_id }
            }).promise();

            if (!mediaResult.Item) {
                return createResponse(404, { error: 'Media not found' });
            }
        }

        const reflexId = uuidv4();
        const reflex = {
            id: reflexId,
            post_id: id,
            user_id: userId,
            media_id: media_id || null,
            flare_data: flare_data || null,
            text_overlay: text_overlay || null,
            reflex_type,
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        await dynamodb.put({
            TableName: REFLEXES_TABLE,
            Item: reflex
        }).promise();

        // Update post reflex count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id },
            UpdateExpression: 'ADD reflexes :inc',
            ExpressionAttributeValues: { ':inc': 1 }
        }).promise();

        // Fetch user data for the response
        try {
            const userResult = await dynamodb.get({
                TableName: USERS_TABLE,
                Key: { id: userId }
            }).promise();

            if (userResult.Item) {
                reflex.username = userResult.Item.username;
                reflex.display_name = userResult.Item.display_name;
                reflex.avatar_url = userResult.Item.avatar_url;
            }
        } catch (error) {
            console.error('Error fetching user data for response:', error);
            // Continue without user data in response
        }

        return createResponse(201, {
            message: 'Reflex created successfully',
            reflex
        });

    } catch (error) {
        console.error('CreateReflex error:', error);
        return createResponse(500, { error: 'Failed to create reflex', details: error.message });
    }
};

// Delete a reflex
const deleteReflex = async (event) => {
    try {
        const { id } = event.pathParameters;
        const userId = getUserIdFromEvent(event);

        // Get the reflex to verify ownership
        const reflexResult = await dynamodb.get({
            TableName: REFLEXES_TABLE,
            Key: { id }
        }).promise();

        if (!reflexResult.Item) {
            return createResponse(404, { error: 'Reflex not found' });
        }

        const reflex = reflexResult.Item;

        // Check if user owns the reflex
        if (reflex.user_id !== userId) {
            return createResponse(403, { error: 'Not authorized to delete this reflex' });
        }

        const postId = reflex.post_id;

        // Delete the reflex
        await dynamodb.delete({
            TableName: REFLEXES_TABLE,
            Key: { id }
        }).promise();

        // Update post reflex count
        await dynamodb.update({
            TableName: POSTS_TABLE,
            Key: { id: postId },
            UpdateExpression: 'ADD reflexes :dec',
            ExpressionAttributeValues: { ':dec': -1 }
        }).promise();

        return createResponse(200, { message: 'Reflex deleted successfully' });

    } catch (error) {
        console.error('DeleteReflex error:', error);
        return createResponse(500, { error: 'Failed to delete reflex', details: error.message });
    }
};

// Main handler
exports.handler = async (event) => {
    console.log('Event:', JSON.stringify(event, null, 2));

    const { httpMethod, path, pathParameters } = event;

    try {
        // Handle CORS preflight
        if (httpMethod === 'OPTIONS') {
            return createResponse(200, { message: 'CORS preflight' });
        }

        // Route requests
        if (httpMethod === 'GET' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await getReflexes(event);
        } else if (httpMethod === 'POST' && path.includes('/posts/') && path.includes('/reflexes')) {
            return await createReflex(event);
        } else if (httpMethod === 'DELETE' && path.includes('/reflexes/')) {
            return await deleteReflex(event);
        } else {
            return createResponse(404, { error: 'Route not found' });
        }

    } catch (error) {
        console.error('Handler error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
