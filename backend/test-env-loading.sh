#!/bin/bash

# Test script to verify environment loading works correctly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# Get environment from command line or default to development
TEST_ENVIRONMENT=${1:-${ENVIRONMENT:-development}}
export ENVIRONMENT="$TEST_ENVIRONMENT"

print_header "Testing environment loading for: $TEST_ENVIRONMENT"

# Load the environment loading function from seed-data script
script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the load_env_file function from seed-data.sh
source <(sed -n '/^load_env_file()/,/^}/p' "$script_dir/scripts/seed-data.sh")

# Test loading
if load_env_file; then
    print_status "✅ Environment loading successful"
    
    print_header "Environment Variables Loaded:"
    echo "ENVIRONMENT: ${ENVIRONMENT:-NOT_SET}"
    echo "PROJECT_NAME: ${PROJECT_NAME:-NOT_SET}"
    echo "AWS_REGION: ${AWS_REGION:-NOT_SET}"
    echo "R2_ACCOUNT_ID: ${R2_ACCOUNT_ID:0:10}... (truncated)"
    echo "R2_ACCESS_KEY_ID: ${R2_ACCESS_KEY_ID:0:10}... (truncated)"
    echo "CLOUDFLARE_API_TOKEN: ${CLOUDFLARE_API_TOKEN:0:10}... (truncated)"
    
    print_header "Testing AWS Connection:"
    if aws sts get-caller-identity --region "${AWS_REGION:-us-west-2}" >/dev/null 2>&1; then
        print_status "✅ AWS connection successful"
        aws sts get-caller-identity --region "${AWS_REGION:-us-west-2}" --output table
    else
        print_error "❌ AWS connection failed"
        print_status "Check your AWS credentials: aws configure list"
    fi
    
else
    print_error "❌ Environment loading failed"
    exit 1
fi
