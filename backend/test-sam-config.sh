#!/bin/bash

# Test SAM configuration for different environments

echo "🔍 Testing SAM Configuration"
echo "============================"

# Test development config
echo ""
echo "📋 Development Configuration:"
sam deploy --config-env default --parameter-overrides "CertificateArn=test" --no-execute-changeset --no-confirm-changeset 2>&1 | grep -E "(stack|Stack|parameter|Parameter)" | head -10

# Test staging config  
echo ""
echo "📋 Staging Configuration:"
sam deploy --config-env staging --parameter-overrides "CertificateArn=test" --no-execute-changeset --no-confirm-changeset 2>&1 | grep -E "(stack|Stack|parameter|Parameter)" | head -10

# Show samconfig.toml staging section
echo ""
echo "📋 samconfig.toml [staging] section:"
sed -n '/\[staging\.deploy\.parameters\]/,/\[.*\]/p' samconfig.toml | head -10

echo ""
echo "✅ Configuration test complete"
