import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/image_resolution.dart';
import '../utils/app_logger.dart';
import '../utils/image_utils.dart';
import 'flare_editor_screen.dart';

class ImageEditorScreen extends StatefulWidget {
  final File imageFile;

  const ImageEditorScreen({super.key, required this.imageFile});

  @override
  State<ImageEditorScreen> createState() => _ImageEditorScreenState();
}

class _ImageEditorScreenState extends State<ImageEditorScreen> {
  final ImageResolution _targetResolution = ImageResolution.fixedResolution;
  Size? _imageSize;
  bool _isLoading = false;
  bool _isUploading = false;

  // Image transformation controller for zoom and pan
  final TransformationController _transformationController =
      TransformationController();

  @override
  void initState() {
    super.initState();
    _loadImageDimensions();
  }

  void _initializeImageTransform() {
    // Reset transformation to show the whole image initially
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _transformationController.value = Matrix4.identity();
      }
    });
  }

  Future<void> _loadImageDimensions() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final dimensions = await ImageUtils.getImageDimensions(widget.imageFile);
      setState(() {
        _imageSize = dimensions;
      });

      // Initialize the image transform after dimensions are loaded
      _initializeImageTransform();
    } catch (e) {
      AppLogger.error('Error loading image dimensions', error: e);
      _showErrorSnackBar('Failed to load image');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _processAndContinue() async {
    if (_imageSize == null) return;

    try {
      setState(() {
        _isUploading = true;
      });

      // Show loading dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(
                child: Card(
                  color: AppColors.gfDarkBackground100,
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: AppColors.gfGreen),
                        SizedBox(height: 16),
                        Text(
                          'Processing image...',
                          style: TextStyle(
                            color: AppColors.gfOffWhite,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
        );
      }

      // Add minimum loading time to ensure user sees the loading dialog
      await Future.delayed(const Duration(milliseconds: 500));

      // Calculate crop area based on the current transformation
      final cropRect = _calculateCropArea();

      // Process the image with black background for blank space
      final processedFile = await ImageUtils.processImageWithBackground(
        originalFile: widget.imageFile,
        targetResolution: _targetResolution,
        cropArea: cropRect,
        originalImageSize: _imageSize!,
        backgroundColor: Colors.black,
      );

      if (mounted) {
        // Close loading dialog
        Navigator.of(context).pop();

        // Navigate to flare editor screen with slide transition
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    FlareEditorScreen(croppedImageFile: processedFile),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.easeInOut;

              var tween = Tween(
                begin: begin,
                end: end,
              ).chain(CurveTween(curve: curve));

              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Close loading dialog if it's open
        Navigator.of(context).pop();
      }
      AppLogger.error('Error processing image', error: e);
      _showErrorSnackBar('Failed to process image');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Rect _calculateCropArea() {
    // Get the transformation matrix
    final matrix = _transformationController.value;

    // Extract translation and scale from the matrix
    final translation = matrix.getTranslation();
    final scale = matrix.getMaxScaleOnAxis();

    // Get the container dimensions (9:16 aspect ratio) - must match preview exactly
    final availableWidth =
        MediaQuery.of(context).size.width - 48; // Account for margins
    final containerWidth = availableWidth * 0.7; // 70% of available width
    final containerHeight =
        containerWidth * (16 / 9); // 9:16 ratio (height = width * 16/9)

    // Calculate the visible area in normalized coordinates (0.0 to 1.0)
    // The transformation matrix tells us how the image is positioned and scaled
    final normalizedX = (-translation.x / scale) / containerWidth;
    final normalizedY = (-translation.y / scale) / containerHeight;
    final normalizedWidth = containerWidth / (containerWidth * scale);
    final normalizedHeight = containerHeight / (containerHeight * scale);

    // Clamp values to ensure they're within bounds
    return Rect.fromLTWH(
      normalizedX.clamp(0.0, 1.0),
      normalizedY.clamp(0.0, 1.0),
      normalizedWidth.clamp(0.0, 1.0 - normalizedX.clamp(0.0, 1.0)),
      normalizedHeight.clamp(0.0, 1.0 - normalizedY.clamp(0.0, 1.0)),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.darkBlue,
      appBar: AppBar(
        title: const Text(
          'Edit Image',
          style: TextStyle(
            color: AppColors.gfGreen,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.darkBlue,
        iconTheme: const IconThemeData(color: AppColors.gfOffWhite),
        elevation: 0,
        actions: [
          if (!_isUploading)
            TextButton(
              onPressed: _imageSize != null ? _processAndContinue : null,
              child: const Text(
                'Next',
                style: TextStyle(
                  color: AppColors.gfGreen,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: AppColors.gfGreen),
              )
              : SafeArea(
                child: Column(
                  children: [
                    // Instructions
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const Text(
                            'Crop to 9:16 Ratio',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: AppColors.gfOffWhite,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Pinch to zoom, drag to move the image within the frame',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.gfGrayText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Image preview with 16:9 crop area
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 24),
                        child: _buildImageCropPreview(),
                      ),
                    ),

                    // Upload button and loading indicator
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          if (_isUploading)
                            const CircularProgressIndicator(
                              color: AppColors.gfGreen,
                            )
                          else
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed:
                                    _imageSize != null
                                        ? _processAndContinue
                                        : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppColors.gfGreen,
                                  foregroundColor: AppColors.darkBlue,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: const Text(
                                  'Continue',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildImageCropPreview() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate 9:16 aspect ratio container (portrait)
        final containerWidth =
            constraints.maxWidth * 0.7; // Use 70% of available width
        final containerHeight =
            containerWidth * (16 / 9); // 9:16 ratio (height/width)

        return Center(
          child: Container(
            width: containerWidth,
            height: containerHeight,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.gfGreen, width: 2),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Stack(
                children: [
                  // Main image with zoom/pan
                  InteractiveViewer(
                    transformationController: _transformationController,
                    minScale: 0.1,
                    maxScale: 4.0,
                    panEnabled: true,
                    scaleEnabled: true,
                    boundaryMargin: EdgeInsets.zero,
                    constrained: true,
                    child: SizedBox(
                      width: containerWidth,
                      height: containerHeight,
                      child: FittedBox(
                        fit: BoxFit.contain,
                        child: Image.file(widget.imageFile),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
