import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../services/aws_auth_service.dart';

class AddCommentWidget extends StatefulWidget {
  final String postId;
  final Function(String content) onCommentAdded;
  final bool isLoading;

  const AddCommentWidget({
    super.key,
    required this.postId,
    required this.onCommentAdded,
    this.isLoading = false,
  });

  @override
  State<AddCommentWidget> createState() => _AddCommentWidgetState();
}

class _AddCommentWidgetState extends State<AddCommentWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  static const int _maxLength = 500;

  @override
  void initState() {
    super.initState();
    // Listen to text changes to rebuild the widget
    _controller.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _submitComment() {
    final content = _controller.text.trim();
    if (content.isNotEmpty && !widget.isLoading) {
      widget.onCommentAdded(content);
      _controller.clear();
      _focusNode.unfocus();
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = AwsAuthService.instance.currentUser;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        border: Border(
          top: BorderSide(color: AppColors.gfGrayBorder, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // User avatar
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: [AppColors.gfLightTeal, AppColors.gfTeal],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: const Icon(
                    Icons.person,
                    size: 20,
                    color: AppColors.gfDarkBlue,
                  ),
                ),

                const SizedBox(width: 12),

                // Text input field
                Expanded(
                  child: Container(
                    constraints: const BoxConstraints(
                      minHeight: 40,
                      maxHeight: 120,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.gfCardBackground,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.gfGrayBorder.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      maxLength: _maxLength,
                      maxLines: null,
                      textInputAction: TextInputAction.newline,
                      style: const TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 14,
                      ),
                      decoration: InputDecoration(
                        hintText: 'Add a comment...',
                        hintStyle: const TextStyle(
                          color: AppColors.gfGrayText,
                          fontSize: 14,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                        counterText: '', // Hide the character counter
                      ),
                      onSubmitted: (_) => _submitComment(),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Send button
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient:
                        _controller.text.trim().isNotEmpty && !widget.isLoading
                            ? const LinearGradient(
                              colors: [AppColors.gfGreen, AppColors.gfTeal],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                            : null,
                    color:
                        _controller.text.trim().isEmpty || widget.isLoading
                            ? AppColors.gfGrayBorder.withValues(alpha: 0.3)
                            : null,
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(18),
                      onTap:
                          _controller.text.trim().isNotEmpty &&
                                  !widget.isLoading
                              ? _submitComment
                              : null,
                      child: Center(
                        child:
                            widget.isLoading
                                ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.gfOffWhite,
                                    ),
                                  ),
                                )
                                : Icon(
                                  Icons.send,
                                  size: 18,
                                  color:
                                      _controller.text.trim().isNotEmpty
                                          ? AppColors.gfOffWhite
                                          : AppColors.gfGrayText,
                                ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Character count and user info
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 48),
              child: Row(
                children: [
                  Text(
                    'Commenting as ${currentUser?.username ?? 'User'}',
                    style: const TextStyle(
                      color: AppColors.gfGrayText,
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      final length = _controller.text.length;
                      final isNearLimit = length > _maxLength * 0.8;

                      return Text(
                        '$length/$_maxLength',
                        style: TextStyle(
                          color:
                              isNearLimit
                                  ? (length >= _maxLength
                                      ? Colors.red
                                      : Colors.orange)
                                  : AppColors.gfGrayText,
                          fontSize: 12,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
